<script setup>
import { onMounted, ref } from "vue";
import baseService from "@/service/baseService";
import PoDetailDialog from "/src/views/yuqing/po-detail-dialog.vue";
const loading = ref(false);
const dateList = ref([]);
const videoList = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const size = ref("default");
const background = ref(true);
const disabled = ref(false);
const total = ref(0);
const poDetailDialog = ref();
const labelList = ref([]);
const platformList = ref([]);
const riskList = ref([]);
const checkboxGroup1 = ref("999");
const checkboxGroup2 = ref("999");
const checkboxGroup3 = ref("999");

const getPlatform = (platformId) => {
  const platform = {
    "-1": "未指定",
    1: "微博",
    2: "微信",
    3: "抖音",
  };
  return platform[platformId];
};

const getLabel = (labelId) => {
  const label = {
    0: "人身伤害",
    1: "交通事故",
    2: "自然灾害",
    3: "校园事件",
    4: "医疗卫生",
    5: "环境污染",
    6: "国家安全",
    7: "宗教文化",
    8: "其他负面",
    9: "非负样本",
    "-1": "未指定"
  };
  return label[labelId];
};

const getRisk = (riskId) => {
  const risk = {
    0: "低风险",
    1: "中风险",
    2: "高风险",
    "-1": "未指定"
  };
  return risk[riskId];
};

const getRiskColor = (riskId) => {
  const color = {
    0: "darkturquoise",
    1: "orange",
    2: "red"
  };
  return color[riskId];
};

const changeLabel = (value) => {
  currentPage.value = 1;
  console.log("label:"+value);
  getData();
};

const changeRisk = (value) => {
  currentPage.value = 1;
    console.log("risk:"+value);
  getData();
};

const changePlatform = (value) => {
  currentPage.value = 1;
    console.log("platform:"+value);
  getData();
};

function mergeVidUrlToDateList(dateArr, videoArr) {
  return dateArr.map(item => {
    const video = videoArr.find(v => v.id === item.videoId);
    return {
      ...item,
      vidUrl: video ? video.vidUrl : null
    };
  });
}

const getData = () => {
  loading.value = true;
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    labelId: checkboxGroup1.value === "999" ? null : checkboxGroup1.value,
    riskId: checkboxGroup2.value === "999" ? null : checkboxGroup2.value,
    platformId: checkboxGroup3.value === "999" ? null : checkboxGroup3.value
  };
  baseService.get("/vsafety/video/page", params).then((res) => {
    // 这里用 videoList.value 合并
    dateList.value = mergeVidUrlToDateList(res.data.list, videoList.value);
    total.value = res.data.total;
    loading.value = false;
  });
};
/* 当前页改变时的回调*/
const handleCurrentChange = (val) => {
  currentPage.value = val;
  getData();
};

/* 每页条数改变时的回调*/
const handleSizeChange = (val) => {
  currentPage.value = 1;
  pageSize.value = val;
  getData();
};

const handleDialog = (id, context, resource, type, grade, color) => {
  const detailParams = {
    id,
    context,
    resource,
    type,
    grade,
    color
  };
  poDetailDialog.value.init(detailParams);
};

onMounted(() => {
  Promise.all([
    baseService.get("vsafety/video/page"),
    baseService.get("vsafety/label/page"),
    baseService.get("vsafety/platform/page", { page: 1, size: 1000 }),
    baseService.get("vsafety/risk/page", { page: 1, size: 1000 }),
    baseService.get("vsafety/video/page")
  ]).then(([dateArray, labelArray, platformArray, riskArray, videoArry]) => {
    videoList.value = videoArry.data.list;
    console.log('视频列表', videoList.value);
    dateList.value = mergeVidUrlToDateList(dateArray.data.list, videoList.value);
    labelList.value = labelArray.data.list;
    platformList.value = platformArray.data.list; 
    riskList.value = riskArray.data.list;
    total.value = dateArray.data.total;
    console.log('合并后dateList', dateList.value);
  });
});
</script>

<template>
  <div class="header">
    <el-header>
      <div class="header-content">
        <div class="yuqing-label">标注分类</div>
        <el-radio-group v-model="checkboxGroup1" size="small" @change="changeLabel">
          <el-radio-button value="999" style="margin-top: 5px; margin-right: 10px"> 全部 </el-radio-button>
          <el-radio-button v-for="item in labelList" :key="item.id" :value="item.id.toString()" style="margin-right: 10px">{{ item.label }}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="header-content">
        <div class="yuqing-label">风险分类</div>
        <el-radio-group v-model="checkboxGroup2" size="small" @change="changeRisk">
          <el-radio-button value="999" style="margin-top: 5px; margin-right: 10px"> 全部 </el-radio-button>
          <el-radio-button v-for="item in riskList" :key="item.id" :value="item.id.toString()" style="margin-right: 10px">{{ item.risk }}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="header-content">
        <div class="yuqing-label">平台分类</div>
        <el-radio-group v-model="checkboxGroup3" size="small" @change="changePlatform">
          <el-radio-button value="999" style="margin-top: 5px; margin-right: 10px"> 全部 </el-radio-button>
          <el-radio-button v-for="item in platformList" :key="item.id" :value="item.id.toString()" style="margin-right: 10px">{{ getPlatform(item.id) }}</el-radio-button>
        </el-radio-group>
      </div>
    </el-header>
  </div>
  <div class="container">
    <el-container>
      <el-main v-loading="loading">
        <div v-show="dateList.length <= 0" style="text-align: center">暂无数据 !</div>
        <div class="video-grid">
          <div v-for="item in videoList" :key="item.id" class="video-box">
            <div class="video-thumb" @click="handleDialog(item.id, item.context, getPlatform(item.platformId), getLabel(item.labelId), getRisk(item.riskId), getRiskColor(item.riskId))">
              <video
                v-if="item.vidUrl"
                :src="item.vidUrl"
                controls
                preload="auto"
                class="video-player"
                @click.stop="handleDialog(item.id, item.context, getPlatform(item.platformId), getLabel(item.labelId), getRisk(item.riskId), getRiskColor(item.riskId))"
              ></video>
              <div v-else class="no-video">无视频</div>
            </div>
            <div class="video-info"  @click.stop="handleDialog(item.id, item.context, getPlatform(item.platformId), getLabel(item.labelId), getRisk(item.riskId), getRiskColor(item.riskId))">
              <div class="video-title" @click="handleDialog(item.id, item.context, getPlatform(item.platformId), getLabel(item.labelId), getRisk(item.riskId), getRiskColor(item.riskId))">
                {{ item.context }}
              </div>
              <div class="video-meta">
                <el-tag :color="getRiskColor(item.riskId)">{{ getRisk(item.riskId) }}</el-tag>
              </div>
              <div class="video-footer">
                <span>来源：{{ getPlatform(item.platformId) }}</span>
                <span>事件类型：{{ getLabel(item.labelId) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
    <!-- 弹窗 舆情详情 -->
    <po-detail-dialog ref="poDetailDialog"></po-detail-dialog>
  </div>
  <div class="demo-pagination-block">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :size="size"
      :disabled="disabled"
      :background="background"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped>
.el-header {
  background-color: #ebebeb80;
  padding: 10px 15px;
  height: 100%;
  line-height: 3;
}
.el-main {
  margin-top: 50px;
  background-color: #ebebeb80;
}
.demo-button-style {
  position: relative;
  top: -33px;
  left: 80px;
}
.demo-button-style {
  margin-top: 24px;
}
.header-content {
  display: flex;
  align-items: center;
}
.yuqing-label {
  margin-right: 10px;
  min-width: 70px;
}
.detail-item {
  /* margin-top: 20px; */
  padding: 10px;
  border-radius: 5px;
  /* background-color: #fff; */
}
.detail-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-content {
  font-size: 14px;
  line-height: 2;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-footer {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}
.el-tag {
  margin-right: 20px;
  width: 80px;
}
.detail-title-left {
  cursor: pointer;
  color: #677fef;
}
.detail-title-left:hover {
  color: #4060ee;
}
.demo-pagination-block + .demo-pagination-block {
  margin-top: 10px;
}
.demo-pagination-block .demonstration {
  margin-bottom: 16px;
}
.common-header {
  text-align: center;
  margin-bottom: 30px;
}
.search-btn {
  background-color: #5b4cfe !important;
  color: #fff !important;
  border-radius: 0 5px 5px 0 !important;
}
.search-btn:hover {
  background-color: #7f71ff !important;
}
.video-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24px;
  margin: 30px 0;
}
.video-box {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px #0000000a;
  padding: 16px 12px 12px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s;
  cursor: pointer;
  min-height: 260px;
  position: relative;
}
.video-box:hover {
  box-shadow: 0 4px 16px #409eff33;
}
.video-thumb {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}
.video-player {
  width: 100%;
  max-height: 120px;
  object-fit: cover;
  border-radius: 6px;
  background: #000;
}
.no-video {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bbb;
  background: #f5f5f5;
  border-radius: 6px;
}
.video-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.video-title {
  font-size: 15px;
  font-weight: bold;
  color: #4060ee;
  margin-bottom: 8px;
  cursor: pointer;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.video-title:hover {
  color: #1a3bb3;
}
.video-meta {
  margin-bottom: 8px;
}
.video-footer {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
@media (max-width: 1200px) {
  .video-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 800px) {
  .video-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 500px) {
  .video-grid {
    grid-template-columns: 1fr;
  }
}
</style>
