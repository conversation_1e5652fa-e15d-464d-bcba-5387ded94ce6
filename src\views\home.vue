<template>
  <el-row :gutter="20">
    <!-- 数据概览模块 -->
    <el-col :span="24">
      <div class="grid-content ep-bg-purple">
        <span class="title">数据概览</span>
        <el-row :gutter="24" style="margin-top: 15px;">
          <el-col :span="6">
            <div class="data-card">
              <div class="data-overview">
                <el-progress 
                  type="circle" 
                  :percentage="100" 
                  color="#5470C6"
                  style="--el-progress-circle-width: 80px;"
                >
                  <template #default="{ percentage }">
                    <el-icon color="#5470C6" size="40px"><Tickets /></el-icon>
                  </template>
                </el-progress>
              </div>
              <div class="message">
                <span class="message-label">全部信息</span>
                <h3 class="message-value">{{ allCount }}</h3>
              </div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="data-card">
              <div class="data-overview">
                <el-progress 
                  type="circle" 
                  :percentage="percentage1" 
                  color="red"
                  style="--el-progress-circle-width: 80px;"
                >
                  <template #default="{ percentage }">
                    <el-icon color="red" size="40px"><CloseBold /></el-icon>
                  </template>
                </el-progress>
              </div>
              <div class="message">
                <span class="message-label">负面信息</span>
                <h3 class="message-value">{{ badCount }}</h3>
                <el-progress 
                  :percentage="percentage1" 
                  color="red" 
                  stroke-width="4"
                  style="margin-top: 6px;"
                />
              </div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="data-card">
              <div class="data-overview">
                <el-progress 
                  type="circle" 
                  :percentage="percentage2" 
                  color="green"
                  style="--el-progress-circle-width: 80px;"
                >
                  <template #default="{ percentage }">
                    <el-icon color="green" size="40px"><Tickets /></el-icon>
                  </template>
                </el-progress>
              </div>
              <div class="message">
                <span class="message-label">正面信息</span>
                <h3 class="message-value">{{ goodCount }}</h3>
                <el-progress 
                  :percentage="percentage2" 
                  color="green" 
                  stroke-width="4"
                  style="margin-top: 6px;"
                />
              </div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="data-card">
              <div class="data-overview">
                <el-progress 
                  type="circle" 
                  :percentage="percentage3" 
                  color="#FFBC34"
                  style="--el-progress-circle-width: 80px;"
                >
                  <template #default="{ percentage }">
                    <el-icon color="#FFBC34" size="40px"><Bell /></el-icon>
                  </template>
                </el-progress>
              </div>
              <div class="message">
                <span class="message-label">中性信息</span>
                <h3 class="message-value">{{ neutralCount }}</h3>
                <el-progress 
                  :percentage="percentage3" 
                  color="#FFBC34" 
                  stroke-width="4"
                  style="margin-top: 6px;"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-col>
  </el-row>

  <el-row :gutter="20" style="margin-top: 20px;">
    <!-- 最新资讯模块 -->
    <el-col :span="12">
      <div class="grid-content ep-bg-purple hight">
        <span class="title">最新资讯</span>
        <div 
          v-for="item in filterDetailList" 
          :key="item.id" 
          class="detail-item"
        >
          <div class="detail-title">
            <div 
              class="detail-title-left" 
              @click="to(item.url)"
            >
              {{ item.name.substring(0, 40) }}
            </div>
            <span 
              class="detail-title-right" 
              :style="{ color: getColor(item.natureId) }"
            >
              <span 
                style="background-color: rgba(255, 255, 255, 0.562); padding: 2px; font-size: 12px"
              >
                {{ getNature(item.natureId) }}
              </span>
            </span>
          </div>
          <div class="detail-footer">
            {{ item.time }} {{ item.source }}
          </div>
        </div>
      </div>
    </el-col>

    <!-- 情感占比模块 -->
    <el-col :span="12">
      <div class="grid-content ep-bg-purple hight">
        <span class="title">情感占比</span> 
        <div id="emotional_ratio" class="emotional_ratio"></div>
        <div class="emotional-ratio-info">
          <div class="ratio-item">
            <div class="ratio-value" style="color: green">{{ percentage2 }}%</div>
            <div class="ratio-label">正面</div>
          </div>
          <div class="ratio-item">
            <div class="ratio-value" style="color: #FFBC34">{{ percentage3 }}%</div>
            <div class="ratio-label">中性</div>
          </div>
          <div class="ratio-item">
            <div class="ratio-value" style="color: red">{{ percentage1 }}%</div>
            <div class="ratio-label">负面</div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script setup>
import router from "@/router";
import baseService from "@/service/baseService";
import * as echarts from "echarts";
import { onMounted, ref } from "vue";
// 引入 Element Plus 图标（根据实际引入方式调整，这里假设是按需引入）
import { Tickets, CloseBold, Bell } from "@element-plus/icons-vue"; 

const detail = ref("");
//总数量
const allCount = ref(0);
//负面数量
const badCount = ref(0);
//正面数量
const goodCount = ref(0);
//中性数量
const neutralCount = ref(0);
const percentage = ref(0);
const percentage1 = ref(0);
const percentage2 = ref(0);
const percentage3 = ref(0);
const natureList = ref([]);
const filterDetailList = ref([]);

const getColor = (natureId) => {
  const color = {
    1: "orange",
    2: "red",
    3: "green",
    4: "blue"
  };
  return color[natureId];
};

const getNature = (natureId) => {
  return natureList.value.find((item) => item.id == natureId)?.name || "";
};

baseService.get("vsafety/nature/page").then((res) => {
  natureList.value = res.data.list;
});

//跳转到详情页
const to = (url) => {
  router.push({ path: "/vsafety/envdetail", state: { url } });
};

baseService.get("es", { size: 1 }).then((res) => {
  detail.value = res.data.content;
  filterDetailList.value = detail.value.slice(0, 4).sort((a, b) => new Date(b.time) - new Date(a.time));
  allCount.value = res.data.numberOfElements;
  badCount.value = res.data.content.filter((item) => item.natureId == 2).length;
  goodCount.value = res.data.content.filter((item) => item.natureId == 3).length;
  neutralCount.value = res.data.content.filter((item) => item.natureId == 1).length;

  percentage1.value = Number(((badCount.value / allCount.value) * 100).toFixed(2));
  percentage2.value = Number(((goodCount.value / allCount.value) * 100).toFixed(2));
  percentage3.value = Number(((neutralCount.value / allCount.value) * 100).toFixed(2));

  const myChart = echarts.init(document.getElementById("emotional_ratio"));
  myChart.setOption({
    tooltip: {
      trigger: "item"
    },
    series: [
      {
        name: "情感占比",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: goodCount.value, name: "正面", itemStyle: { color: 'green' } },
          { value: neutralCount.value, name: "中性", itemStyle: { color: '#FFBC34' } },
          { value: badCount.value, name: "负面", itemStyle: { color: 'red' } }
        ]
      }
    ]
  });
});

onMounted(() => {
  // 可在这里做一些初始化操作，比如监听窗口变化重新渲染图表等
  window.addEventListener('resize', () => {
    const myChart = echarts.getInstanceByDom(document.getElementById("emotional_ratio"));
    myChart && myChart.resize();
  });
});
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}
.el-row:last-child {
  margin-bottom: 0;
}
.el-col {
  border-radius: 5px;
}

.grid-content {
  border-radius: 5px;
  min-height: 160px;
  background-color: #f3f5f6;
  padding: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  display: block;
  margin-bottom: 10px;
}

/* 数据概览卡片样式 */
.data-card {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px rgba(84, 112, 198, 0.2);
  transition: all 0.3s ease;
}
.data-card:hover {
  box-shadow: 0 5px 15px rgba(84, 112, 198, 0.3);
  transform: translateY(-3px);
}
.data-overview {
  margin-right: 15px;
}
.message {
  display: flex;
  flex-direction: column;
}
.message-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}
.message-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

/* 最新资讯样式 */
.hight {
  height: 400px;
}
.detail-item {
  margin-top: 15px;
  padding: 10px;
  border-radius: 5px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(84, 112, 198, 0.15);
  transition: all 0.3s ease;
}
.detail-item:hover {
  box-shadow: 0 5px 15px rgba(84, 112, 198, 0.25);
  transform: translateY(-2px);
}
.detail-title {
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
.detail-title-left {
  cursor: pointer;
  color: #333;
  transition: color 0.3s ease;
}
.detail-title-left:hover {
  color: #5470c6;
}
.detail-title-right {
  width: 80px;
  text-align: center;
}
.detail-footer {
  font-size: 12px;
  color: #999;
  display: flex;
  justify-content: space-between;
}

/* 情感占比样式 */
.emotional_ratio {
  width: 220px;
  height: 220px;
  margin: 0 auto 15px;
}
.emotional-ratio-info {
  display: flex;
  justify-content: center;
  gap: 30px;
}
.ratio-item {
  text-align: center;
}
.ratio-value {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}
.ratio-label {
  font-size: 14px;
  color: #666;
}
</style>