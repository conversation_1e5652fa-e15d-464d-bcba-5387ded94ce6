<template>
  <div class="rr-login">
    <!-- 粒子背景 -->
    <div class="particles-container">
      <div class="particle" v-for="n in 50" :key="n" :style="getParticleStyle()"></div>
    </div>

    <!-- 科技网格背景 -->
    <div class="tech-grid"></div>

    <!-- 登录表单容器 -->
    <div class="login-container">
      <div class="login-form-wrapper">
        <h1 class="system-title">视频分析管理平台</h1>
        <div class="login-form">
          <h4 class="login-title">登录</h4>
          <el-form ref="formRef" label-width="0" :status-icon="true" :model="login" :rules="rules" @keyup.enter="onLogin">
            <el-form-item prop="username">
              <el-input
                v-model="login.username"
                size="large"
                placeholder="请输入用户账号"
                prefix-icon="user"
                autocomplete="off"
                class="tech-input">
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                placeholder="请输入用户密码"
                v-model="login.password"
                size="large"
                prefix-icon="lock"
                autocomplete="off"
                show-password
                class="tech-input">
              </el-input>
            </el-form-item>
            <el-form-item prop="captcha">
              <div class="captcha-wrapper">
                <el-input
                  v-model="login.captcha"
                  size="large"
                  placeholder="验证码"
                  prefix-icon="first-aid-kit"
                  class="tech-input captcha-input">
                </el-input>
                <img
                  class="captcha-image"
                  :src="state.captchaUrl"
                  @click="onRefreshCode"
                  alt="验证码" />
              </div>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :disabled="state.loading"
                @click="onLogin"
                class="tech-login-btn"
                :loading="state.loading">
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { CacheToken } from "@/constants/cacheKey";
import baseService from "@/service/baseService";
import { setCache } from "@/utils/cache";
import { ElMessage } from "element-plus";
import { getUuid } from "@/utils/utils";
import app from "@/constants/app";
import SvgIcon from "@/components/base/svg-icon/index";
import { useAppStore } from "@/store";
import { useRouter } from "vue-router";

const store = useAppStore();
const router = useRouter();

// 生成随机粒子样式
const getParticleStyle = () => {
  return {
    left: Math.random() * 100 + '%',
    top: Math.random() * 100 + '%',
    animationDelay: Math.random() * 20 + 's',
    animationDuration: (Math.random() * 10 + 10) + 's'
  };
};

const state = reactive({
  captchaUrl: "",
  loading: false,
  year: new Date().getFullYear()
});

const login = reactive({ username: "", password: "", captcha: "", uuid: "" });

onMounted(() => {
  //清理数据
  store.logout();
  getCaptchaUrl();
});
const formRef = ref();

const rules = ref({
  username: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  password: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  captcha: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
});

const getCaptchaUrl = () => {
  login.uuid = getUuid();
  state.captchaUrl = `${app.api}/captcha?uuid=${login.uuid}`;
};

const onRefreshCode = () => {
  getCaptchaUrl();
};

const onLogin = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      state.loading = true;
      baseService
        .post("/login", login)
        .then((res) => {
          state.loading = false;
          if (res.code === 0) {
            // 登录成功逻辑
            setCache(CacheToken, res.data, true);
            ElMessage.success("登录成功");
            router.push("/");
          } else {
            login.captcha = ""; // 清空验证码
            onRefreshCode();    // 刷新验证码图片
            ElMessage.error(res.msg);
          }
        })
        .catch(() => {
          state.loading = false;
          login.captcha = ""; // 清空验证码
          onRefreshCode();   // 刷新验证码图片
        });
    }
  });
};
</script>

<style lang="less" scoped>
@import url("@/assets/theme/base.less");

.rr-login {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0c1445 0%, #1a237e 25%, #283593 50%, #3949ab 75%, #5c6bc0 100%);
  display: flex;
  align-items: center;
  justify-content: center;

  // 粒子容器
  .particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
  }

  // 粒子样式
  .particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: float linear infinite;
    box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);

    &:nth-child(odd) {
      background: rgba(64, 196, 255, 0.8);
      box-shadow: 0 0 6px rgba(64, 196, 255, 0.8);
    }

    &:nth-child(3n) {
      width: 1px;
      height: 1px;
      background: rgba(255, 255, 255, 0.6);
    }

    &:nth-child(4n) {
      width: 3px;
      height: 3px;
      background: rgba(100, 200, 255, 0.7);
      animation-duration: 15s;
    }
  }

  // 科技网格背景
  .tech-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: 1;
  }

  // 登录容器
  .login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 450px;
    padding: 20px;
  }

  // 登录表单包装器
  .login-form-wrapper {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: slideInUp 0.8s ease-out;
  }

  // 系统标题
  .system-title {
    color: #ffffff;
    font-size: 28px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 40px;
    letter-spacing: 2px;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    background: linear-gradient(45deg, #ffffff, #64c4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  // 登录标题
  .login-title {
    color: #ffffff;
    font-size: 24px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 30px;
    letter-spacing: 1px;
  }

  // 验证码包装器
  .captcha-wrapper {
    display: flex;
    gap: 12px;
    align-items: center;

    .captcha-input {
      flex: 1;
    }

    .captcha-image {
      width: 120px;
      height: 40px;
      border-radius: 8px;
      cursor: pointer;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(64, 196, 255, 0.5);
        box-shadow: 0 0 10px rgba(64, 196, 255, 0.3);
      }
    }
  }

  // 科技感输入框
  :deep(.tech-input) {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(64, 196, 255, 0.4);
        background: rgba(255, 255, 255, 0.12);
      }

      &.is-focus {
        border-color: #40c4ff;
        background: rgba(255, 255, 255, 0.15);
        box-shadow:
          inset 0 2px 4px rgba(0, 0, 0, 0.1),
          0 0 0 2px rgba(64, 196, 255, 0.2);
      }
    }

    .el-input__inner {
      color: #ffffff;
      font-size: 16px;

      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .el-input__prefix-inner {
      color: rgba(255, 255, 255, 0.7);
    }

    .el-input__suffix-inner {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  // 科技感登录按钮
  .tech-login-btn {
    width: 100%;
    height: 50px;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 2px;
    margin-top: 20px;
    background: linear-gradient(45deg, #1976d2, #40c4ff);
    border: none;
    border-radius: 12px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(64, 196, 255, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(64, 196, 255, 0.4);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
    }

    &.is-loading {
      background: linear-gradient(45deg, #666, #888);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .login-container {
      max-width: 90%;
      padding: 10px;
    }

    .login-form-wrapper {
      padding: 30px 20px;
    }

    .system-title {
      font-size: 24px;
      margin-bottom: 30px;
    }
  }
}

// 动画定义
@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 覆盖 Element Plus 的默认样式
:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item__error) {
  color: #ff6b6b;
  font-size: 12px;
  background: rgba(255, 107, 107, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 4px;
}
</style>
